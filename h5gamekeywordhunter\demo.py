#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
H5游戏关键词猎手 - 演示版本
不依赖外部API，使用模拟数据展示功能
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import random
from typing import List, Dict, Any

# 页面配置
st.set_page_config(
    page_title="H5游戏关键词猎手 - 演示版",
    page_icon="🎮",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .status-running {
        color: #28a745;
    }
    .status-stopped {
        color: #dc3545;
    }
    .keyword-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin-bottom: 0.5rem;
    }
    .demo-notice {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def generate_demo_keywords() -> List[Dict[str, Any]]:
    """生成演示关键词数据"""
    keywords = [
        "wordle clone", "2048 game", "snake game", "tetris online", "puzzle game",
        "idle clicker", "match 3 game", "tower defense", "io game", "casual game",
        "brain teaser", "word game", "number puzzle", "color match", "memory game",
        "reaction game", "strategy game", "arcade game", "retro game", "mini game"
    ]
    
    sources = ["Reddit", "YouTube", "Twitter", "TikTok"]
    
    demo_data = []
    for i, keyword in enumerate(keywords):
        demo_data.append({
            'keyword': keyword,
            'source': random.choice(sources),
            'trend_score_7d': random.randint(10, 100),
            'trend_score_30d': random.randint(5, 95),
            'kgr_value': round(random.uniform(0.1, 2.0), 2),
            'search_volume': random.randint(100, 10000),
            'competition': random.choice(['Low', 'Medium', 'High']),
            'collected_date': (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat(),
            'post_score': random.randint(10, 1000),
            'post_comments': random.randint(5, 200)
        })
    
    return demo_data

def main():
    # 演示版本提示
    st.markdown("""
    <div class="demo-notice">
        <h4>🚀 演示版本</h4>
        <p>这是H5游戏关键词猎手的演示版本，使用模拟数据展示功能。完整版本支持：</p>
        <ul>
            <li>Reddit API集成 - 从游戏相关subreddit收集关键词</li>
            <li>YouTube API集成 - 分析热门游戏视频标题</li>
            <li>Google Trends分析 - 获取关键词趋势数据</li>
            <li>Notion数据库同步 - 自动保存分析结果</li>
            <li>SEO关键词分析 - KGR值计算和竞争度分析</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)
    
    # 主标题
    st.markdown('<h1 class="main-header">🎮 H5游戏关键词猎手 - 演示版</h1>', unsafe_allow_html=True)
    
    # 初始化演示数据
    if 'demo_keywords' not in st.session_state:
        st.session_state.demo_keywords = generate_demo_keywords()
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 演示控制")
        
        if st.button("🔄 生成新数据", type="primary"):
            st.session_state.demo_keywords = generate_demo_keywords()
            st.success("已生成新的演示数据！")
        
        if st.button("📊 添加随机关键词"):
            new_keyword = {
                'keyword': f"game {random.randint(1000, 9999)}",
                'source': random.choice(["Reddit", "YouTube", "Twitter"]),
                'trend_score_7d': random.randint(10, 100),
                'trend_score_30d': random.randint(5, 95),
                'kgr_value': round(random.uniform(0.1, 2.0), 2),
                'search_volume': random.randint(100, 10000),
                'competition': random.choice(['Low', 'Medium', 'High']),
                'collected_date': datetime.now().isoformat(),
                'post_score': random.randint(10, 1000),
                'post_comments': random.randint(5, 200)
            }
            st.session_state.demo_keywords.append(new_keyword)
            st.success("已添加新关键词！")
        
        st.divider()
        
        # 过滤选项
        st.header("🔍 数据过滤")
        
        source_filter = st.selectbox("来源过滤", 
            ["全部"] + list(set([k['source'] for k in st.session_state.demo_keywords])))
        
        min_trend = st.slider("最小趋势评分", 0, 100, 0)
        max_kgr = st.slider("最大KGR值", 0.0, 2.0, 2.0, 0.1)
    
    # 主内容区域
    tab1, tab2, tab3, tab4 = st.tabs(["📊 仪表板", "🔍 关键词列表", "📈 趋势分析", "ℹ️ 关于"])
    
    with tab1:
        st.header("📊 数据仪表板")
        
        # 过滤数据
        filtered_data = st.session_state.demo_keywords
        if source_filter != "全部":
            filtered_data = [k for k in filtered_data if k['source'] == source_filter]
        filtered_data = [k for k in filtered_data if k['trend_score_7d'] >= min_trend]
        filtered_data = [k for k in filtered_data if k['kgr_value'] <= max_kgr]
        
        # 统计指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总关键词数", len(filtered_data), "📈")
        
        with col2:
            high_trend_count = len([k for k in filtered_data if k['trend_score_7d'] > 70])
            st.metric("高趋势关键词", high_trend_count, "🔥")
        
        with col3:
            low_kgr_count = len([k for k in filtered_data if k['kgr_value'] < 0.25])
            st.metric("低竞争关键词", low_kgr_count, "🎯")
        
        with col4:
            avg_score = sum([k['trend_score_7d'] for k in filtered_data]) / len(filtered_data) if filtered_data else 0
            st.metric("平均趋势评分", f"{avg_score:.1f}", "📊")
        
        # 数据可视化
        if filtered_data:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("📈 关键词来源分布")
                df = pd.DataFrame(filtered_data)
                source_counts = df['source'].value_counts()
                fig_pie = px.pie(values=source_counts.values, names=source_counts.index, 
                               title="关键词来源分布")
                st.plotly_chart(fig_pie, use_container_width=True)
            
            with col2:
                st.subheader("📊 趋势评分分布")
                fig_hist = px.histogram(df, x='trend_score_7d', 
                                      title="7天趋势评分分布", nbins=10)
                st.plotly_chart(fig_hist, use_container_width=True)
            
            st.subheader("🎯 KGR值 vs 趋势评分")
            fig_scatter = px.scatter(df, x='kgr_value', y='trend_score_7d',
                                   hover_data=['keyword', 'source'],
                                   title="KGR值 vs 7天趋势评分",
                                   color='source')
            st.plotly_chart(fig_scatter, use_container_width=True)
    
    with tab2:
        st.header("🔍 关键词列表")
        
        # 过滤数据
        filtered_data = st.session_state.demo_keywords
        if source_filter != "全部":
            filtered_data = [k for k in filtered_data if k['source'] == source_filter]
        filtered_data = [k for k in filtered_data if k['trend_score_7d'] >= min_trend]
        filtered_data = [k for k in filtered_data if k['kgr_value'] <= max_kgr]
        
        # 排序选项
        sort_by = st.selectbox("排序方式", ["趋势评分", "KGR值", "搜索量", "收集时间"])
        
        if sort_by == "趋势评分":
            filtered_data = sorted(filtered_data, key=lambda x: x['trend_score_7d'], reverse=True)
        elif sort_by == "KGR值":
            filtered_data = sorted(filtered_data, key=lambda x: x['kgr_value'])
        elif sort_by == "搜索量":
            filtered_data = sorted(filtered_data, key=lambda x: x['search_volume'], reverse=True)
        else:
            filtered_data = sorted(filtered_data, key=lambda x: x['collected_date'], reverse=True)
        
        # 显示关键词表格
        if filtered_data:
            df = pd.DataFrame(filtered_data)
            st.dataframe(
                df[['keyword', 'source', 'trend_score_7d', 'kgr_value', 'search_volume', 'competition']],
                use_container_width=True
            )
        else:
            st.info("没有符合条件的关键词")
    
    with tab3:
        st.header("📈 趋势分析")
        
        if st.session_state.demo_keywords:
            df = pd.DataFrame(st.session_state.demo_keywords)
            
            # 趋势对比图
            fig_scatter = px.scatter(df, x='trend_score_7d', y='trend_score_30d',
                                   hover_data=['keyword', 'source'],
                                   title="7天 vs 30天趋势对比",
                                   color='source')
            st.plotly_chart(fig_scatter, use_container_width=True)
            
            # 热门关键词排行
            st.subheader("🔥 热门关键词排行")
            top_keywords = df.nlargest(10, 'trend_score_7d')[['keyword', 'trend_score_7d', 'source', 'kgr_value']]
            st.dataframe(top_keywords, use_container_width=True)
            
            # 竞争度分析
            st.subheader("🎯 竞争度分析")
            competition_counts = df['competition'].value_counts()
            fig_bar = px.bar(x=competition_counts.index, y=competition_counts.values,
                           title="关键词竞争度分布")
            st.plotly_chart(fig_bar, use_container_width=True)
    
    with tab4:
        st.header("ℹ️ 关于H5游戏关键词猎手")
        
        st.markdown("""
        ### 🎯 项目简介
        H5游戏关键词猎手是一个专门为H5游戏开发者和营销人员设计的关键词发现和分析工具。
        
        ### ✨ 主要功能
        - **多平台数据收集**: 从Reddit、YouTube等平台收集游戏相关关键词
        - **趋势分析**: 使用Google Trends分析关键词热度变化
        - **SEO分析**: 计算KGR值，评估关键词竞争难度
        - **数据可视化**: 直观展示关键词数据和趋势
        - **自动化同步**: 支持将结果同步到Notion数据库
        
        ### 🚀 技术栈
        - **前端**: Streamlit
        - **数据处理**: Pandas, NumPy
        - **可视化**: Plotly, Altair
        - **API集成**: Reddit API, YouTube API, Google Trends
        - **数据存储**: Notion API
        
        ### 📦 安装完整版本
        ```bash
        pip install praw notion-client pytrends google-api-python-client
        ```
        
        ### 🔧 配置说明
        完整版本需要配置以下API密钥：
        - Reddit API (client_id, client_secret)
        - YouTube Data API (api_key)
        - Notion API (token, database_id)
        - Google Custom Search API (可选)
        
        ### 📞 联系方式
        如需完整版本或技术支持，请联系开发团队。
        """)

if __name__ == "__main__":
    main()
