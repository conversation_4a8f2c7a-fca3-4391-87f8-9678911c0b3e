from googleapiclient.discovery import build
import requests
from bs4 import BeautifulSoup
import re
import logging
from typing import Dict, Any, List
from datetime import datetime
import time
import random

class SEOAnalyzer:
    def __init__(self, config):
        self.config = config
        self.google_config = config.get_google_search_config()
        self.search_service = None
        self.setup_google_search()
        
        # 大站域名列表
        self.major_sites = [
            'wikipedia.org', 'youtube.com', 'facebook.com', 'twitter.com',
            'instagram.com', 'reddit.com', 'amazon.com', 'ebay.com',
            'cnn.com', 'bbc.com', 'nytimes.com', 'washingtonpost.com',
            'forbes.com', 'techcrunch.com', 'wired.com', 'theverge.com',
            'ign.com', 'gamespot.com', 'polygon.com', 'kotaku.com',
            'steam.com', 'twitch.tv', 'discord.com'
        ]
        
        # 缓存机制
        self.cache = {}
        self.cache_duration = 3600  # 1小时缓存
    
    def setup_google_search(self):
        """设置Google Custom Search API"""
        try:
            api_key = self.google_config.get('api_key')
            if not api_key or api_key == 'YOUR_GOOGLE_SEARCH_API_KEY':
                logging.warning("Google Search API密钥未配置，SEO分析功能将受限")
                return
            
            self.search_service = build('customsearch', 'v1', developerKey=api_key)
            logging.info("Google Custom Search API连接成功")
        except Exception as e:
            logging.error(f"Google Custom Search API连接失败: {str(e)}")
            self.search_service = None
    
    def analyze_keyword(self, keyword: str) -> Dict[str, Any]:
        """分析关键词的SEO数据"""
        # 检查缓存
        cache_key = f"seo_{keyword}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
        
        seo_data = {
            'serp_has_images': False,
            'serp_has_major_sites': False,
            'allintitle_count': 0,
            'kgr_value': 0.0,
            'domain_available': False,
            'seo_analysis_date': datetime.now().isoformat()
        }
        
        try:
            # 分析SERP结果
            serp_data = self._analyze_serp(keyword)
            seo_data.update(serp_data)
            
            # 获取allintitle数量
            allintitle_count = self._get_allintitle_count(keyword)
            seo_data['allintitle_count'] = allintitle_count
            
            # 计算KGR值
            kgr_value = self._calculate_kgr(keyword, allintitle_count)
            seo_data['kgr_value'] = kgr_value
            
            # 检查域名可用性
            domain_available = self._check_domain_availability(keyword)
            seo_data['domain_available'] = domain_available
            
            # 缓存结果
            self.cache[cache_key] = (seo_data, time.time())
            
        except Exception as e:
            logging.error(f"SEO分析失败 (关键词: {keyword}): {str(e)}")
        
        return seo_data
    
    def _analyze_serp(self, keyword: str) -> Dict[str, Any]:
        """分析搜索结果页面"""
        serp_data = {
            'serp_has_images': False,
            'serp_has_major_sites': False,
            'serp_results': []
        }
        
        if not self.search_service:
            return serp_data
        
        try:
            search_engine_id = self.google_config.get('search_engine_id')
            if not search_engine_id:
                return serp_data
            
            # 执行搜索
            result = self.search_service.cse().list(
                q=keyword,
                cx=search_engine_id,
                num=10
            ).execute()
            
            if 'items' not in result:
                return serp_data
            
            # 分析搜索结果
            for item in result['items']:
                result_data = {
                    'title': item.get('title', ''),
                    'link': item.get('link', ''),
                    'snippet': item.get('snippet', ''),
                    'displayLink': item.get('displayLink', '')
                }
                serp_data['serp_results'].append(result_data)
                
                # 检查是否有图片
                if 'pagemap' in item and 'cse_image' in item['pagemap']:
                    serp_data['serp_has_images'] = True
                
                # 检查是否有大站
                domain = item.get('displayLink', '').lower()
                if any(major_site in domain for major_site in self.major_sites):
                    serp_data['serp_has_major_sites'] = True
            
            time.sleep(random.uniform(1, 2))  # API限制
            
        except Exception as e:
            logging.error(f"SERP分析失败: {str(e)}")
        
        return serp_data
    
    def _get_allintitle_count(self, keyword: str) -> int:
        """获取allintitle搜索结果数量"""
        if not self.search_service:
            return 0
        
        try:
            search_engine_id = self.google_config.get('search_engine_id')
            if not search_engine_id:
                return 0
            
            # 执行allintitle搜索
            allintitle_query = f'allintitle:"{keyword}"'
            result = self.search_service.cse().list(
                q=allintitle_query,
                cx=search_engine_id,
                num=1
            ).execute()
            
            # 获取结果总数
            if 'searchInformation' in result:
                total_results = result['searchInformation'].get('totalResults', '0')
                return int(total_results)
            
            time.sleep(random.uniform(1, 2))  # API限制
            
        except Exception as e:
            logging.error(f"获取allintitle数量失败: {str(e)}")
        
        return 0
    
    def _calculate_kgr(self, keyword: str, allintitle_count: int) -> float:
        """计算KGR值 (Keyword Golden Ratio)"""
        try:
            # 估算月搜索量 (这里使用简化的估算方法)
            # 在实际应用中，你可能需要使用Google Ads API或其他工具获取准确的搜索量
            estimated_monthly_searches = self._estimate_search_volume(keyword)
            
            if estimated_monthly_searches == 0:
                return 0.0
            
            kgr = allintitle_count / estimated_monthly_searches
            return round(kgr, 3)
            
        except Exception as e:
            logging.error(f"计算KGR失败: {str(e)}")
            return 0.0
    
    def _estimate_search_volume(self, keyword: str) -> int:
        """估算关键词月搜索量"""
        # 这是一个简化的估算方法
        # 在实际应用中，建议使用Google Ads API或其他专业工具
        
        # 基于关键词长度和类型的简单估算
        word_count = len(keyword.split())
        keyword_lower = keyword.lower()
        
        base_volume = 1000  # 基础搜索量
        
        # 根据词汇数量调整
        if word_count == 1:
            base_volume *= 5
        elif word_count == 2:
            base_volume *= 2
        elif word_count >= 4:
            base_volume *= 0.5
        
        # 根据关键词类型调整
        if any(term in keyword_lower for term in ['game', 'play', 'online']):
            base_volume *= 1.5
        
        if any(term in keyword_lower for term in ['viral', 'trending', 'popular']):
            base_volume *= 2
        
        if any(term in keyword_lower for term in ['new', 'latest', '2024']):
            base_volume *= 1.2
        
        return max(int(base_volume), 100)  # 最小100搜索量
    
    def _check_domain_availability(self, keyword: str) -> bool:
        """检查域名可用性"""
        try:
            # 生成可能的域名
            domain_name = re.sub(r'[^a-zA-Z0-9]', '', keyword.lower())
            if len(domain_name) < 3:
                return False
            
            domain = f"{domain_name}.com"
            
            # 简单的域名可用性检查
            # 注意：这只是一个基础检查，实际应用中可能需要更专业的域名查询服务
            try:
                response = requests.head(f"http://{domain}", timeout=5)
                return False  # 域名已被注册
            except requests.exceptions.RequestException:
                return True  # 域名可能可用
                
        except Exception as e:
            logging.error(f"检查域名可用性失败: {str(e)}")
            return False
    
    def analyze_competition(self, keyword: str) -> Dict[str, Any]:
        """分析关键词竞争度"""
        competition_data = {
            'competition_level': 'unknown',
            'top_competitors': [],
            'competition_score': 0.0
        }
        
        if not self.search_service:
            return competition_data
        
        try:
            search_engine_id = self.google_config.get('search_engine_id')
            if not search_engine_id:
                return competition_data
            
            # 搜索关键词
            result = self.search_service.cse().list(
                q=keyword,
                cx=search_engine_id,
                num=10
            ).execute()
            
            if 'items' not in result:
                return competition_data
            
            major_site_count = 0
            competitors = []
            
            for item in result['items']:
                domain = item.get('displayLink', '').lower()
                competitors.append({
                    'domain': domain,
                    'title': item.get('title', ''),
                    'is_major_site': any(major_site in domain for major_site in self.major_sites)
                })
                
                if any(major_site in domain for major_site in self.major_sites):
                    major_site_count += 1
            
            # 计算竞争评分
            competition_score = (major_site_count / 10) * 100
            
            # 确定竞争等级
            if competition_score >= 70:
                competition_level = 'high'
            elif competition_score >= 40:
                competition_level = 'medium'
            else:
                competition_level = 'low'
            
            competition_data.update({
                'competition_level': competition_level,
                'top_competitors': competitors,
                'competition_score': round(competition_score, 2),
                'major_sites_count': major_site_count
            })
            
        except Exception as e:
            logging.error(f"分析竞争度失败: {str(e)}")
        
        return competition_data
    
    def get_related_keywords(self, keyword: str) -> List[str]:
        """获取相关关键词建议"""
        related_keywords = []
        
        if not self.search_service:
            return related_keywords
        
        try:
            # 使用Google搜索建议
            # 这里可以集成其他关键词工具的API
            
            # 基于关键词生成相关词汇
            base_terms = keyword.split()
            
            # 添加常见的游戏相关后缀
            suffixes = ['game', 'online', 'free', 'play', 'browser', 'html5', 'io']
            prefixes = ['new', 'best', 'top', 'popular', 'viral']
            
            for suffix in suffixes:
                if suffix not in keyword.lower():
                    related_keywords.append(f"{keyword} {suffix}")
            
            for prefix in prefixes:
                if prefix not in keyword.lower():
                    related_keywords.append(f"{prefix} {keyword}")
            
            # 限制返回数量
            return related_keywords[:10]
            
        except Exception as e:
            logging.error(f"获取相关关键词失败: {str(e)}")
            return related_keywords
    
    def batch_analyze_keywords(self, keywords: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量分析关键词SEO数据"""
        results = {}
        
        for i, keyword in enumerate(keywords):
            logging.info(f"SEO分析 {i+1}/{len(keywords)}: {keyword}")
            
            try:
                seo_data = self.analyze_keyword(keyword)
                results[keyword] = seo_data
                
                # 添加延迟避免API限制
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                logging.error(f"批量SEO分析失败 (关键词: {keyword}): {str(e)}")
                results[keyword] = {
                    'serp_has_images': False,
                    'serp_has_major_sites': False,
                    'allintitle_count': 0,
                    'kgr_value': 0.0,
                    'domain_available': False,
                    'seo_analysis_date': datetime.now().isoformat()
                }
        
        return results
