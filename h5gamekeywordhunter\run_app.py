#!/usr/bin/env python3
"""
H5游戏关键词猎手 - 启动脚本
运行命令: python run_app.py
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    print(f"✓ Python version: {sys.version}")

def install_requirements():
    """Install dependencies"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("Error: requirements.txt file not found")
        sys.exit(1)

    print("Installing dependencies...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"Error: Failed to install dependencies - {e}")
        print("Please run manually: pip install -r requirements.txt")
        sys.exit(1)

def check_config():
    """检查配置文件"""
    config_file = Path("config.json")
    if not config_file.exists():
        print("Error: config.json configuration file not found")
        print("Please run: python setup_config.py to configure API keys")
        sys.exit(1)
    
    # Check configuration content
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Check required configuration items
        required_configs = {
            'reddit': ['client_id', 'client_secret'],
            'youtube': ['api_key'],
            'notion': ['token', 'database_id']
        }
        
        missing_configs = []
        for section, keys in required_configs.items():
            if section not in config:
                missing_configs.append(f"{section} section missing")
                continue

            for key in keys:
                if key not in config[section] or config[section][key].startswith('YOUR_'):
                    missing_configs.append(f"{section}.{key}")

        if missing_configs:
            print("Warning: The following configuration items need to be set:")
            for config_item in missing_configs:
                print(f"  - {config_item}")
            print("\nPlease edit config.json file and enter the correct API keys")

            # Ask whether to continue
            response = input("\nContinue to start the application? (y/N): ").strip().lower()
            if response != 'y':
                sys.exit(1)
        else:
            print("✓ Configuration file check passed")
            
    except Exception as e:
        print(f"Error: Configuration file format error - {e}")
        sys.exit(1)

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'data', 'cache']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ Directory structure created")

def run_streamlit_app():
    """Run Streamlit application"""
    print("\n" + "="*50)
    print("H5 Game Keyword Hunter")
    print("="*50)
    print("Starting Web interface...")
    print("The application will open automatically in your browser")
    print("If it doesn't open automatically, visit: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    print("="*50 + "\n")

    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\nApplication stopped")
    except Exception as e:
        print(f"Error: Failed to start application - {e}")
        sys.exit(1)

def main():
    """Main function"""
    print("H5 Game Keyword Hunter - Startup Check")
    print("-" * 40)

    # Check Python version
    check_python_version()

    # Create directories
    create_directories()

    # Check configuration
    check_config()

    # Install dependencies
    install_requirements()

    # Run application
    run_streamlit_app()

if __name__ == "__main__":
    main()
