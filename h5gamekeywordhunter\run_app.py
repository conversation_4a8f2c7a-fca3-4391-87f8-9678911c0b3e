#!/usr/bin/env python3
"""
H5游戏关键词猎手 - 启动脚本
运行命令: python run_app.py
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✓ Python版本: {sys.version}")

def install_requirements():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("错误: requirements.txt文件不存在")
        sys.exit(1)
    
    print("正在安装依赖包...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"错误: 安装依赖包失败 - {e}")
        print("请手动运行: pip install -r requirements.txt")
        sys.exit(1)

def check_config():
    """检查配置文件"""
    config_file = Path("config.json")
    if not config_file.exists():
        print("错误: config.json配置文件不存在")
        print("请复制config.json.example并填入您的API密钥")
        sys.exit(1)
    
    # 检查配置内容
    try:
        import json
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_configs = {
            'reddit': ['client_id', 'client_secret'],
            'youtube': ['api_key'],
            'notion': ['token', 'database_id']
        }
        
        missing_configs = []
        for section, keys in required_configs.items():
            if section not in config:
                missing_configs.append(f"{section}节缺失")
                continue
            
            for key in keys:
                if key not in config[section] or config[section][key].startswith('YOUR_'):
                    missing_configs.append(f"{section}.{key}")
        
        if missing_configs:
            print("警告: 以下配置项需要设置:")
            for config_item in missing_configs:
                print(f"  - {config_item}")
            print("\n请编辑config.json文件并填入正确的API密钥")
            
            # 询问是否继续
            response = input("\n是否继续启动应用? (y/N): ").strip().lower()
            if response != 'y':
                sys.exit(1)
        else:
            print("✓ 配置文件检查通过")
            
    except Exception as e:
        print(f"错误: 配置文件格式错误 - {e}")
        sys.exit(1)

def create_directories():
    """创建必要的目录"""
    directories = ['logs', 'data', 'cache']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    print("✓ 目录结构创建完成")

def run_streamlit_app():
    """运行Streamlit应用"""
    print("\n" + "="*50)
    print("🎮 H5游戏关键词猎手")
    print("="*50)
    print("正在启动Web界面...")
    print("应用将在浏览器中自动打开")
    print("如果没有自动打开，请访问: http://localhost:8501")
    print("按 Ctrl+C 停止应用")
    print("="*50 + "\n")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"错误: 启动应用失败 - {e}")
        sys.exit(1)

def main():
    """主函数"""
    print("H5游戏关键词猎手 - 启动检查")
    print("-" * 30)
    
    # 检查Python版本
    check_python_version()
    
    # 创建目录
    create_directories()
    
    # 检查配置
    check_config()
    
    # 安装依赖
    install_requirements()
    
    # 运行应用
    run_streamlit_app()

if __name__ == "__main__":
    main()
