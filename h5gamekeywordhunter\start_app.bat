@echo off
setlocal enabledelayedexpansion

:: Set console to UTF-8
chcp 65001 >nul 2>&1

:: Set window title
title H5 Game Keyword Hunter

:: Clear screen
cls

echo.
echo ==========================================
echo    H5 Game Keyword Hunter
echo ==========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    goto :error_exit
)

echo [INFO] Python version:
python --version

:: Check if requirements.txt exists
if not exist "requirements.txt" (
    echo [ERROR] requirements.txt not found
    echo Please make sure you are in the correct directory
    echo.
    goto :error_exit
)

:: Check if config.json exists
if not exist "config.json" (
    echo [WARNING] config.json not found
    echo Running configuration wizard...
    echo.
    python setup_config.py
    if %errorlevel% neq 0 (
        echo [ERROR] Configuration failed
        goto :error_exit
    )
)

:: Install dependencies
echo [INFO] Checking dependencies...
pip install -r requirements.txt --quiet
if %errorlevel% neq 0 (
    echo [ERROR] Failed to install dependencies
    echo Please run: pip install -r requirements.txt
    echo.
    goto :error_exit
)

:: Start the application
echo [INFO] Starting H5 Game Keyword Hunter...
echo [INFO] The application will open in your browser
echo [INFO] URL: http://localhost:8501
echo [INFO] Press Ctrl+C to stop the application
echo.
echo ==========================================
echo.

python run_app.py

:: Check if application started successfully
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Application failed to start
    echo.
    echo Troubleshooting steps:
    echo 1. Run: python test_components.py
    echo 2. Check config.json settings
    echo 3. Verify API keys are configured
    echo.
)

goto :normal_exit

:error_exit
echo.
echo [ERROR] Startup failed
echo.
echo For help, please:
echo 1. Read README.md
echo 2. Run: python test_components.py
echo 3. Check the documentation
echo.

:normal_exit
echo.
echo Press any key to exit...
pause >nul
