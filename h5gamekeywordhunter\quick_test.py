#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test script to verify basic functionality
"""

import sys
import json
from pathlib import Path

def test_python_version():
    """Test Python version"""
    print("Testing Python version...")
    if sys.version_info >= (3, 8):
        print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    else:
        print(f"✗ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} (need 3.8+)")
        return False

def test_files():
    """Test if required files exist"""
    print("\nTesting required files...")
    required_files = [
        "main.py",
        "config.py", 
        "config.json",
        "requirements.txt",
        "notion_sync.py"
    ]
    
    all_exist = True
    for file in required_files:
        if Path(file).exists():
            print(f"✓ {file}")
        else:
            print(f"✗ {file} - missing")
            all_exist = False
    
    return all_exist

def test_directories():
    """Test if required directories exist"""
    print("\nTesting required directories...")
    required_dirs = [
        "collectors",
        "analyzers"
    ]
    
    all_exist = True
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"✓ {dir_name}/")
        else:
            print(f"✗ {dir_name}/ - missing")
            all_exist = False
    
    return all_exist

def test_config():
    """Test configuration file"""
    print("\nTesting configuration...")
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            config = json.load(f)
        
        required_sections = ["reddit", "youtube", "notion", "monitoring"]
        missing_sections = []
        
        for section in required_sections:
            if section in config:
                print(f"✓ {section} section")
            else:
                print(f"✗ {section} section - missing")
                missing_sections.append(section)
        
        # Check if API keys are configured
        configured_apis = []
        if (config.get("reddit", {}).get("client_id", "").startswith("YOUR_") == False and
            config.get("reddit", {}).get("client_secret", "").startswith("YOUR_") == False):
            configured_apis.append("Reddit")
        
        if config.get("youtube", {}).get("api_key", "").startswith("YOUR_") == False:
            configured_apis.append("YouTube")
        
        if (config.get("notion", {}).get("token", "").startswith("YOUR_") == False and
            config.get("notion", {}).get("database_id", "").startswith("YOUR_") == False):
            configured_apis.append("Notion")
        
        if configured_apis:
            print(f"✓ Configured APIs: {', '.join(configured_apis)}")
        else:
            print("⚠ No APIs configured yet")
            print("  Run: python setup_config.py")
        
        return len(missing_sections) == 0
        
    except FileNotFoundError:
        print("✗ config.json - file not found")
        return False
    except json.JSONDecodeError as e:
        print(f"✗ config.json - invalid JSON: {e}")
        return False
    except Exception as e:
        print(f"✗ config.json - error: {e}")
        return False

def test_imports():
    """Test if key modules can be imported"""
    print("\nTesting module imports...")
    
    modules_to_test = [
        ("json", "json"),
        ("pathlib", "pathlib"),
        ("requests", "requests"),
        ("pandas", "pandas"),
        ("streamlit", "streamlit")
    ]
    
    all_imported = True
    for module_name, import_name in modules_to_test:
        try:
            __import__(import_name)
            print(f"✓ {module_name}")
        except ImportError:
            print(f"✗ {module_name} - not installed")
            all_imported = False
    
    if not all_imported:
        print("\n  Install missing modules with: pip install -r requirements.txt")
    
    return all_imported

def main():
    """Run all tests"""
    print("=" * 50)
    print("H5 Game Keyword Hunter - Quick Test")
    print("=" * 50)
    
    tests = [
        ("Python Version", test_python_version),
        ("Required Files", test_files),
        ("Required Directories", test_directories),
        ("Configuration", test_config),
        ("Module Imports", test_imports)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} - error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("-" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! You can start the application.")
        print("\nNext steps:")
        print("1. Configure API keys: python setup_config.py")
        print("2. Start the application: python start.py")
        print("3. Or use the batch file: start.bat")
    else:
        print(f"\n⚠ {len(results) - passed} test(s) failed.")
        print("\nPlease fix the issues above before starting the application.")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")
    
    input("\nPress Enter to exit...")
