#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple startup script for H5 Game Keyword Hunter
This script avoids encoding issues in Windows command line
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def main():
    """Main startup function"""
    print("=" * 50)
    print("H5 Game Keyword Hunter")
    print("=" * 50)
    print()
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        input("Press Enter to exit...")
        sys.exit(1)
    
    print(f"Python version: {sys.version}")
    print()
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("Error: main.py not found")
        print("Please make sure you're in the correct directory")
        input("Press Enter to exit...")
        sys.exit(1)
    
    # Check if config exists
    if not Path("config.json").exists():
        print("Configuration file not found. Running setup wizard...")
        try:
            subprocess.run([sys.executable, "setup_config.py"], check=True)
        except subprocess.CalledProcessError:
            print("Configuration setup failed")
            input("Press Enter to exit...")
            sys.exit(1)
        except FileNotFoundError:
            print("setup_config.py not found")
            input("Press Enter to exit...")
            sys.exit(1)
    
    # Install dependencies
    print("Checking dependencies...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--quiet"
        ], check=True)
        print("Dependencies OK")
    except subprocess.CalledProcessError:
        print("Warning: Failed to install some dependencies")
        print("You may need to run: pip install -r requirements.txt")
    
    print()
    print("Starting H5 Game Keyword Hunter...")
    print("The application will open in your browser at: http://localhost:8501")
    print("Press Ctrl+C to stop the application")
    print("=" * 50)
    print()
    
    # Start the application
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "main.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except FileNotFoundError:
        print("Error: Streamlit not found")
        print("Please install it with: pip install streamlit")
        input("Press Enter to exit...")
        sys.exit(1)
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Unexpected error: {e}")
        input("Press Enter to exit...")
