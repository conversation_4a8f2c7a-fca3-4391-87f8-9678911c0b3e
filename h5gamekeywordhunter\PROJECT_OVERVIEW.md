# 🎮 H5游戏关键词猎手 - 项目总览

## 📋 项目简介

H5游戏关键词猎手是一个自动化收集H5游戏关键词并同步到Notion的可视化工具。它能够从多个社交媒体平台收集热门游戏关键词，进行趋势分析和SEO分析，并将结果自动同步到Notion数据库中。

## 🎯 核心功能

### 1. 多平台数据收集
- **Reddit**: 从游戏相关子版块收集热门帖子和关键词
- **YouTube**: 分析游戏视频标题和描述，提取关键词
- **自动去重**: 智能去重和关键词清理

### 2. 智能分析
- **Google Trends**: 分析7天、30天、90天趋势评分
- **SEO分析**: SERP结果分析、allintitle数量、KGR值计算
- **竞争度分析**: 检查大站竞争情况
- **域名可用性**: 检查相关域名注册状态

### 3. 自动化同步
- **Notion集成**: 自动将分析结果同步到Notion数据库
- **批量处理**: 支持批量创建和更新页面
- **数据完整性**: 确保数据结构完整和一致

### 4. 可视化界面
- **现代化Web界面**: 基于Streamlit的响应式界面
- **实时监控**: 实时显示收集状态和统计数据
- **交互式图表**: 趋势分析和数据可视化
- **过滤和搜索**: 灵活的数据过滤和查看功能

## 🏗️ 技术架构

### 核心技术栈
- **Python 3.8+**: 主要开发语言
- **Streamlit**: Web界面框架
- **Pandas**: 数据处理和分析
- **Plotly**: 数据可视化

### API集成
- **Reddit API (PRAW)**: Reddit数据收集
- **YouTube Data API**: YouTube视频分析
- **Google Trends (PyTrends)**: 趋势数据分析
- **Google Custom Search API**: SEO分析
- **Notion API**: 数据同步

### 项目结构
```
h5gamekeywordhunter/
├── main.py                    # 主应用程序
├── config.py                  # 配置管理
├── config.json               # 配置文件
├── notion_sync.py            # Notion同步模块
├── run_app.py                # 启动脚本
├── setup_config.py           # 配置向导
├── test_components.py        # 组件测试
├── start.bat                 # Windows启动脚本
├── requirements.txt          # 依赖包列表
├── collectors/               # 数据收集器
│   ├── __init__.py
│   ├── reddit_collector.py   # Reddit数据收集
│   └── youtube_collector.py  # YouTube数据收集
├── analyzers/                # 分析器
│   ├── __init__.py
│   ├── trends_analyzer.py    # 趋势分析
│   └── seo_analyzer.py       # SEO分析
└── docs/                     # 文档
    ├── README.md
    └── PROJECT_OVERVIEW.md
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Python 3.8+
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置API密钥
```bash
# 运行配置向导
python setup_config.py

# 或手动编辑config.json
```

### 3. 测试组件
```bash
# 运行组件测试
python test_components.py
```

### 4. 启动应用
```bash
# 方式1: 使用启动脚本
python run_app.py

# 方式2: 直接运行Streamlit
streamlit run main.py

# 方式3: Windows批处理 (双击)
start.bat
```

## 📊 数据流程

### 收集阶段
1. **Reddit收集**: 从配置的子版块获取热门帖子
2. **YouTube收集**: 搜索游戏相关视频和趋势内容
3. **关键词提取**: 使用正则表达式和模式匹配提取游戏关键词
4. **数据清理**: 去重、过滤和标准化处理

### 分析阶段
1. **趋势分析**: 调用Google Trends API获取趋势数据
2. **SEO分析**: 使用Google Custom Search分析SERP结果
3. **竞争分析**: 检查大站竞争和域名可用性
4. **评分计算**: 计算KGR值和综合评分

### 同步阶段
1. **数据映射**: 将分析结果映射到Notion数据库字段
2. **批量同步**: 批量创建或更新Notion页面
3. **错误处理**: 处理API限制和同步错误
4. **状态反馈**: 实时显示同步进度和结果

## 🔧 配置说明

### 必需配置
- **Reddit API**: client_id, client_secret
- **YouTube API**: api_key
- **Notion API**: token, database_id

### 可选配置
- **Google Search API**: api_key, search_engine_id (用于SEO分析)
- **监控设置**: 子版块列表、关键词过滤规则
- **分析参数**: 趋势时间范围、评分阈值

### Notion数据库字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| 关键词 | Title | 主键 |
| 来源平台 | Select | Reddit/YouTube |
| 采集日期 | Date | 收集日期 |
| 7天趋势评分 | Number | Google Trends 7天评分 |
| 30天趋势评分 | Number | Google Trends 30天评分 |
| Google Trends 链接 | URL | 趋势分析链接 |
| 是否已被注册域名 | Checkbox | 域名状态 |
| SERP 是否有图片 | Checkbox | 搜索结果图片 |
| SERP 是否有大站 | Checkbox | 大站竞争 |
| allintitle 数值 | Number | 完全匹配结果数 |
| KGR 值 | Number | 关键词黄金比例 |
| 备注 | Text | 附加信息 |

## 📈 使用场景

### 1. H5游戏开发者
- 发现热门游戏趋势和关键词
- 分析市场竞争情况
- 选择合适的游戏主题和关键词

### 2. SEO专家
- 寻找低竞争高潜力关键词
- 分析关键词趋势变化
- 制定SEO策略

### 3. 内容创作者
- 发现热门游戏话题
- 分析用户兴趣趋势
- 优化内容关键词

### 4. 市场研究
- 监控游戏行业趋势
- 分析用户行为模式
- 收集市场情报

## 🔍 监控策略

### Reddit监控
- **目标子版块**: WebGames, browsergames, gaming等
- **关键词模式**: 游戏相关词汇、病毒式传播词汇
- **数据提取**: 帖子标题、内容、评分、评论数

### YouTube监控
- **搜索策略**: 游戏相关关键词搜索
- **趋势视频**: 游戏分类热门视频
- **数据提取**: 视频标题、描述、观看量、互动数据

### 关键词过滤
- **包含模式**: game, play, gaming, browser, html5等
- **排除模式**: console, download, install等
- **长度限制**: 3-50字符
- **去重策略**: 基于关键词内容和来源评分

## 🛠️ 扩展功能

### 计划中的功能
1. **更多平台支持**: TikTok, Twitter, Twitch
2. **高级分析**: 情感分析、用户画像
3. **自动化报告**: 定期生成趋势报告
4. **API接口**: 提供RESTful API
5. **移动端支持**: 响应式设计优化

### 自定义扩展
- **新平台收集器**: 继承基础收集器类
- **自定义分析器**: 添加新的分析维度
- **通知系统**: 集成邮件、Slack等通知
- **数据导出**: 支持CSV、Excel等格式

## 🔒 安全和隐私

### API安全
- 配置文件本地存储
- API密钥加密存储
- 请求频率限制

### 数据隐私
- 不存储个人敏感信息
- 遵循平台API使用条款
- 数据仅用于关键词分析

## 📞 支持和维护

### 故障排除
1. 检查API配置和网络连接
2. 查看日志文件 (app.log)
3. 运行组件测试 (test_components.py)
4. 参考README文档

### 更新和维护
- 定期更新依赖包
- 监控API变更
- 备份配置和数据
- 性能优化

### 社区支持
- GitHub Issues
- 文档和示例
- 用户反馈和建议

---

**项目版本**: v1.0.0  
**最后更新**: 2024年  
**开发语言**: Python  
**许可证**: MIT License
