#!/usr/bin/env python3
"""
组件测试脚本 - 验证各个模块是否正常工作
"""

import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from config import Config
        print("✅ config 模块导入成功")
    except Exception as e:
        print(f"❌ config 模块导入失败: {e}")
        return False
    
    try:
        from collectors.reddit_collector import RedditCollector
        print("✅ reddit_collector 模块导入成功")
    except Exception as e:
        print(f"❌ reddit_collector 模块导入失败: {e}")
        return False
    
    try:
        from collectors.youtube_collector import YouTubeCollector
        print("✅ youtube_collector 模块导入成功")
    except Exception as e:
        print(f"❌ youtube_collector 模块导入失败: {e}")
        return False
    
    try:
        from analyzers.trends_analyzer import TrendsAnalyzer
        print("✅ trends_analyzer 模块导入成功")
    except Exception as e:
        print(f"❌ trends_analyzer 模块导入失败: {e}")
        return False
    
    try:
        from analyzers.seo_analyzer import SEOAnalyzer
        print("✅ seo_analyzer 模块导入成功")
    except Exception as e:
        print(f"❌ seo_analyzer 模块导入失败: {e}")
        return False
    
    try:
        from notion_sync import NotionSync
        print("✅ notion_sync 模块导入成功")
    except Exception as e:
        print(f"❌ notion_sync 模块导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from config import Config
        config = Config()
        
        # 测试配置加载
        reddit_config = config.get_reddit_config()
        youtube_config = config.get_youtube_config()
        notion_config = config.get_notion_config()
        
        print("✅ 配置文件加载成功")
        
        # 检查配置完整性
        missing_configs = []
        
        if (not reddit_config.get('client_id') or 
            reddit_config.get('client_id') == 'YOUR_REDDIT_CLIENT_ID'):
            missing_configs.append("Reddit client_id")
        
        if (not reddit_config.get('client_secret') or 
            reddit_config.get('client_secret') == 'YOUR_REDDIT_CLIENT_SECRET'):
            missing_configs.append("Reddit client_secret")
        
        if (not youtube_config.get('api_key') or 
            youtube_config.get('api_key') == 'YOUR_YOUTUBE_API_KEY'):
            missing_configs.append("YouTube api_key")
        
        if (not notion_config.get('token') or 
            notion_config.get('token') == 'YOUR_NOTION_TOKEN'):
            missing_configs.append("Notion token")
        
        if (not notion_config.get('database_id') or 
            notion_config.get('database_id') == 'YOUR_NOTION_DATABASE_ID'):
            missing_configs.append("Notion database_id")
        
        if missing_configs:
            print(f"⚠️  缺少配置: {', '.join(missing_configs)}")
            print("请运行 'python setup_config.py' 来配置API密钥")
        else:
            print("✅ 所有必要配置已设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_reddit_collector():
    """测试Reddit收集器"""
    print("\n🔴 测试Reddit收集器...")
    
    try:
        from config import Config
        from collectors.reddit_collector import RedditCollector
        
        config = Config()
        collector = RedditCollector(config)
        
        if collector.reddit is None:
            print("⚠️  Reddit API未初始化 (可能是配置问题)")
            return False
        
        # 测试关键词提取
        test_text = "I found this addictive browser game called 'Test Game' that's really fun to play"
        keywords = collector.extract_keywords_from_text(test_text)
        
        if keywords:
            print(f"✅ 关键词提取测试成功: {keywords}")
        else:
            print("⚠️  关键词提取测试未找到关键词")
        
        print("✅ Reddit收集器基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ Reddit收集器测试失败: {e}")
        return False

def test_youtube_collector():
    """测试YouTube收集器"""
    print("\n🔴 测试YouTube收集器...")
    
    try:
        from config import Config
        from collectors.youtube_collector import YouTubeCollector
        
        config = Config()
        collector = YouTubeCollector(config)
        
        if collector.youtube is None:
            print("⚠️  YouTube API未初始化 (可能是配置问题)")
            return False
        
        # 测试关键词提取
        test_text = "Playing the new viral browser game that everyone is talking about"
        keywords = collector.extract_keywords_from_text(test_text)
        
        if keywords:
            print(f"✅ 关键词提取测试成功: {keywords}")
        else:
            print("⚠️  关键词提取测试未找到关键词")
        
        print("✅ YouTube收集器基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ YouTube收集器测试失败: {e}")
        return False

def test_trends_analyzer():
    """测试趋势分析器"""
    print("\n📈 测试趋势分析器...")
    
    try:
        from analyzers.trends_analyzer import TrendsAnalyzer
        
        analyzer = TrendsAnalyzer()
        
        if analyzer.pytrends is None:
            print("⚠️  Google Trends连接失败")
            return False
        
        # 测试获取趋势数据 (使用简单关键词)
        test_keyword = "game"
        trends_data = analyzer.get_trends_data(test_keyword)
        
        if trends_data and 'trend_score_7d' in trends_data:
            print(f"✅ 趋势分析测试成功: 7天评分 = {trends_data['trend_score_7d']}")
        else:
            print("⚠️  趋势分析测试未获取到数据")
        
        print("✅ 趋势分析器基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 趋势分析器测试失败: {e}")
        return False

def test_notion_sync():
    """测试Notion同步"""
    print("\n📝 测试Notion同步...")
    
    try:
        from config import Config
        from notion_sync import NotionSync
        
        config = Config()
        sync = NotionSync(config)
        
        if sync.notion is None:
            print("⚠️  Notion API未初始化 (可能是配置问题)")
            return False
        
        # 测试连接
        if sync.test_connection():
            print("✅ Notion连接测试成功")
        else:
            print("❌ Notion连接测试失败")
            return False
        
        # 测试获取数据库结构
        schema = sync.get_database_schema()
        if schema:
            print(f"✅ 数据库结构获取成功，包含 {len(schema)} 个字段")
        else:
            print("⚠️  无法获取数据库结构")
        
        print("✅ Notion同步基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ Notion同步测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 测试依赖包...")
    
    required_packages = [
        'praw', 'requests', 'notion_client', 'pytrends',
        'googleapiclient', 'streamlit', 'pandas', 'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("\n✅ 所有依赖包已安装")
        return True

def main():
    """主函数"""
    print("🎮 H5游戏关键词猎手 - 组件测试")
    print("=" * 50)
    
    tests = [
        ("依赖包", test_dependencies),
        ("模块导入", test_imports),
        ("配置", test_config),
        ("Reddit收集器", test_reddit_collector),
        ("YouTube收集器", test_youtube_collector),
        ("趋势分析器", test_trends_analyzer),
        ("Notion同步", test_notion_sync)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results[test_name] = False
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    print("-" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！应用可以正常运行。")
        print("运行 'python run_app.py' 启动应用")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败。")
        print("请检查配置和依赖包安装情况。")
        
        if not results.get("配置", False):
            print("\n💡 建议: 运行 'python setup_config.py' 配置API密钥")

if __name__ == "__main__":
    main()
