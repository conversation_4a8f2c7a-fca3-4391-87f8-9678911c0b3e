from pytrends.request import TrendReq
import pandas as pd
import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta
import time
import random

class TrendsAnalyzer:
    def __init__(self):
        self.pytrends = None
        self.setup_pytrends()
        
        # 缓存机制
        self.cache = {}
        self.cache_duration = 3600  # 1小时缓存
    
    def setup_pytrends(self):
        """设置Google Trends连接"""
        try:
            self.pytrends = TrendReq(
                hl='en-US', 
                tz=360,
                timeout=(10, 25),
                retries=2,
                backoff_factor=0.1
            )
            logging.info("Google Trends连接成功")
        except Exception as e:
            logging.error(f"Google Trends连接失败: {str(e)}")
            self.pytrends = None
    
    def get_trends_data(self, keyword: str) -> Dict[str, Any]:
        """获取关键词的趋势数据"""
        if not self.pytrends:
            return self._get_default_trends_data()
        
        # 检查缓存
        cache_key = f"trends_{keyword}"
        if cache_key in self.cache:
            cached_data, timestamp = self.cache[cache_key]
            if time.time() - timestamp < self.cache_duration:
                return cached_data
        
        try:
            # 获取7天趋势
            trend_7d = self._get_trend_score(keyword, 'now 7-d')
            time.sleep(random.uniform(1, 3))  # 随机延迟避免被限制
            
            # 获取30天趋势
            trend_30d = self._get_trend_score(keyword, 'today 1-m')
            time.sleep(random.uniform(1, 3))
            
            # 获取90天趋势
            trend_90d = self._get_trend_score(keyword, 'today 3-m')
            time.sleep(random.uniform(1, 3))
            
            # 获取相关查询
            related_queries = self._get_related_queries(keyword)
            
            trends_data = {
                'trend_score_7d': trend_7d,
                'trend_score_30d': trend_30d,
                'trend_score_90d': trend_90d,
                'google_trends_url': f"https://trends.google.com/trends/explore?q={keyword.replace(' ', '%20')}",
                'related_queries': related_queries,
                'trends_analysis_date': datetime.now().isoformat()
            }
            
            # 缓存结果
            self.cache[cache_key] = (trends_data, time.time())
            
            return trends_data
            
        except Exception as e:
            logging.error(f"获取趋势数据失败 (关键词: {keyword}): {str(e)}")
            return self._get_default_trends_data()
    
    def _get_trend_score(self, keyword: str, timeframe: str) -> float:
        """获取特定时间范围的趋势评分"""
        try:
            self.pytrends.build_payload([keyword], timeframe=timeframe)
            interest_over_time = self.pytrends.interest_over_time()
            
            if interest_over_time.empty:
                return 0.0
            
            # 计算平均趋势评分
            scores = interest_over_time[keyword].values
            valid_scores = [score for score in scores if score > 0]
            
            if not valid_scores:
                return 0.0
            
            # 返回平均值，但给予最近的数据更高权重
            if len(valid_scores) > 1:
                # 最近的数据权重更高
                weights = [i + 1 for i in range(len(valid_scores))]
                weighted_avg = sum(score * weight for score, weight in zip(valid_scores, weights)) / sum(weights)
                return round(weighted_avg, 2)
            else:
                return float(valid_scores[0])
                
        except Exception as e:
            logging.error(f"获取趋势评分失败: {str(e)}")
            return 0.0
    
    def _get_related_queries(self, keyword: str) -> List[str]:
        """获取相关查询"""
        try:
            self.pytrends.build_payload([keyword], timeframe='today 1-m')
            related_queries = self.pytrends.related_queries()
            
            if keyword in related_queries and related_queries[keyword]['top'] is not None:
                top_queries = related_queries[keyword]['top']['query'].head(5).tolist()
                return top_queries
            
            return []
            
        except Exception as e:
            logging.error(f"获取相关查询失败: {str(e)}")
            return []
    
    def _get_default_trends_data(self) -> Dict[str, Any]:
        """返回默认趋势数据"""
        return {
            'trend_score_7d': 0.0,
            'trend_score_30d': 0.0,
            'trend_score_90d': 0.0,
            'google_trends_url': '',
            'related_queries': [],
            'trends_analysis_date': datetime.now().isoformat()
        }
    
    def compare_keywords(self, keywords: List[str], timeframe: str = 'today 1-m') -> Dict[str, Any]:
        """比较多个关键词的趋势"""
        if not self.pytrends or len(keywords) == 0:
            return {}
        
        try:
            # Google Trends一次最多比较5个关键词
            keywords = keywords[:5]
            
            self.pytrends.build_payload(keywords, timeframe=timeframe)
            interest_over_time = self.pytrends.interest_over_time()
            
            if interest_over_time.empty:
                return {}
            
            comparison_data = {}
            for keyword in keywords:
                if keyword in interest_over_time.columns:
                    scores = interest_over_time[keyword].values
                    valid_scores = [score for score in scores if score > 0]
                    
                    if valid_scores:
                        comparison_data[keyword] = {
                            'average_score': round(sum(valid_scores) / len(valid_scores), 2),
                            'max_score': max(valid_scores),
                            'min_score': min(valid_scores),
                            'trend_direction': self._calculate_trend_direction(valid_scores)
                        }
            
            return comparison_data
            
        except Exception as e:
            logging.error(f"比较关键词趋势失败: {str(e)}")
            return {}
    
    def _calculate_trend_direction(self, scores: List[float]) -> str:
        """计算趋势方向"""
        if len(scores) < 2:
            return 'stable'
        
        # 比较前半部分和后半部分的平均值
        mid_point = len(scores) // 2
        first_half_avg = sum(scores[:mid_point]) / mid_point
        second_half_avg = sum(scores[mid_point:]) / (len(scores) - mid_point)
        
        if second_half_avg > first_half_avg * 1.1:
            return 'rising'
        elif second_half_avg < first_half_avg * 0.9:
            return 'falling'
        else:
            return 'stable'
    
    def get_regional_trends(self, keyword: str, geo: str = 'US') -> Dict[str, Any]:
        """获取特定地区的趋势数据"""
        if not self.pytrends:
            return {}
        
        try:
            self.pytrends.build_payload([keyword], timeframe='today 1-m', geo=geo)
            
            # 获取地区兴趣
            interest_by_region = self.pytrends.interest_by_region()
            
            regional_data = {
                'geo': geo,
                'regional_interest': {},
                'analysis_date': datetime.now().isoformat()
            }
            
            if not interest_by_region.empty and keyword in interest_by_region.columns:
                # 获取前10个地区
                top_regions = interest_by_region[keyword].nlargest(10)
                regional_data['regional_interest'] = top_regions.to_dict()
            
            return regional_data
            
        except Exception as e:
            logging.error(f"获取地区趋势失败: {str(e)}")
            return {}
    
    def get_trending_searches(self, geo: str = 'US') -> List[str]:
        """获取当前热门搜索"""
        if not self.pytrends:
            return []
        
        try:
            trending_searches = self.pytrends.trending_searches(pn=geo)
            
            if not trending_searches.empty:
                # 过滤游戏相关的热门搜索
                game_related_terms = []
                for term in trending_searches[0].head(20):
                    term_lower = term.lower()
                    if any(keyword in term_lower for keyword in ['game', 'play', 'gaming', 'online', 'browser']):
                        game_related_terms.append(term)
                
                return game_related_terms[:10]
            
            return []
            
        except Exception as e:
            logging.error(f"获取热门搜索失败: {str(e)}")
            return []
    
    def analyze_keyword_seasonality(self, keyword: str) -> Dict[str, Any]:
        """分析关键词的季节性趋势"""
        if not self.pytrends:
            return {}
        
        try:
            # 获取过去12个月的数据
            self.pytrends.build_payload([keyword], timeframe='today 12-m')
            interest_over_time = self.pytrends.interest_over_time()
            
            if interest_over_time.empty or keyword not in interest_over_time.columns:
                return {}
            
            # 按月分组分析
            monthly_data = interest_over_time.resample('M')[keyword].mean()
            
            seasonality_data = {
                'keyword': keyword,
                'monthly_averages': monthly_data.to_dict(),
                'peak_month': monthly_data.idxmax().strftime('%B'),
                'low_month': monthly_data.idxmin().strftime('%B'),
                'seasonality_score': round(monthly_data.std() / monthly_data.mean(), 2) if monthly_data.mean() > 0 else 0,
                'analysis_date': datetime.now().isoformat()
            }
            
            return seasonality_data
            
        except Exception as e:
            logging.error(f"分析季节性趋势失败: {str(e)}")
            return {}
    
    def batch_analyze_keywords(self, keywords: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量分析关键词"""
        results = {}
        
        for i, keyword in enumerate(keywords):
            logging.info(f"分析关键词 {i+1}/{len(keywords)}: {keyword}")
            
            try:
                trends_data = self.get_trends_data(keyword)
                results[keyword] = trends_data
                
                # 添加延迟避免API限制
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                logging.error(f"批量分析失败 (关键词: {keyword}): {str(e)}")
                results[keyword] = self._get_default_trends_data()
        
        return results
