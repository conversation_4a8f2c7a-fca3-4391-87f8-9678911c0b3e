# 🚀 快速开始指南

## 📋 准备工作

### 1. 系统要求
- Python 3.8 或更高版本
- 稳定的网络连接
- 现代浏览器 (Chrome, Firefox, Safari, Edge)

### 2. 必需的API密钥
在开始之前，您需要获取以下API密钥：

#### Reddit API (必需)
- 访问: https://www.reddit.com/prefs/apps
- 创建应用类型: script
- 获取: client_id 和 client_secret

#### YouTube API (必需)
- 访问: https://console.cloud.google.com/
- 启用: YouTube Data API v3
- 获取: API Key

#### Notion API (必需)
- 访问: https://www.notion.so/my-integrations
- 创建集成并获取: Integration Token
- 创建数据库并获取: Database ID

#### Google Custom Search API (可选，用于SEO分析)
- 访问: https://console.cloud.google.com/
- 启用: Custom Search API
- 创建: 自定义搜索引擎

## 🛠️ 安装步骤

### 步骤 1: 下载项目
```bash
# 如果您有git
git clone <repository-url>
cd h5gamekeywordhunter

# 或者直接下载ZIP文件并解压
```

### 步骤 2: 安装依赖
```bash
# 安装Python依赖包
pip install -r requirements.txt
```

### 步骤 3: 配置API密钥
```bash
# 运行配置向导 (推荐)
python setup_config.py

# 或者手动编辑 config.json 文件
```

### 步骤 4: 测试配置
```bash
# 运行组件测试
python test_components.py
```

### 步骤 5: 启动应用
```bash
# 方式1: 使用启动脚本 (推荐)
python run_app.py

# 方式2: Windows用户可以双击
start.bat

# 方式3: 直接运行Streamlit
streamlit run main.py
```

## 🎯 首次使用

### 1. 打开应用
启动后，应用会自动在浏览器中打开，地址为: http://localhost:8501

### 2. 验证配置
在侧边栏点击 "验证配置" 按钮，确保所有API连接正常。

### 3. 开始收集
- 点击 "立即收集一次" 进行手动收集
- 或点击 "开始收集" 启动自动收集模式

### 4. 查看结果
- 在 "仪表板" 查看统计数据
- 在 "关键词列表" 浏览收集的关键词
- 在 "趋势分析" 查看趋势图表

### 5. 同步到Notion
点击 "同步到Notion" 将数据保存到您的Notion数据库。

## 📊 Notion数据库设置

### 创建数据库
1. 在Notion中创建新的数据库
2. 添加以下字段:

| 字段名 | 类型 | 必需 |
|--------|------|------|
| 关键词 | Title | ✅ |
| 来源平台 | Select | ✅ |
| 采集日期 | Date | ✅ |
| 7天趋势评分 | Number | ⭕ |
| 30天趋势评分 | Number | ⭕ |
| Google Trends 链接 | URL | ⭕ |
| 是否已被注册域名 | Checkbox | ⭕ |
| SERP 是否有图片 | Checkbox | ⭕ |
| SERP 是否有大站 | Checkbox | ⭕ |
| allintitle 数值 | Number | ⭕ |
| KGR 值 | Number | ⭕ |
| 备注 | Text | ⭕ |

### 共享数据库
1. 点击数据库右上角的 "Share" 按钮
2. 添加您创建的集成
3. 给予 "Edit" 权限

### 获取数据库ID
从数据库URL中复制32位字符的ID:
```
https://notion.so/your-workspace/DATABASE_ID?v=...
```

## ⚙️ 配置说明

### config.json 结构
```json
{
  "reddit": {
    "client_id": "您的Reddit客户端ID",
    "client_secret": "您的Reddit客户端密钥"
  },
  "youtube": {
    "api_key": "您的YouTube API密钥"
  },
  "notion": {
    "token": "您的Notion集成令牌",
    "database_id": "您的Notion数据库ID"
  },
  "monitoring": {
    "reddit_subreddits": ["WebGames", "browsergames", "gaming"],
    "keywords_to_track": ["browser game", "html5 game"],
    "max_posts_per_subreddit": 20
  }
}
```

### 自定义监控
您可以修改 `monitoring` 部分来自定义:
- 监控的Reddit子版块
- 关键词过滤规则
- 收集数量限制

## 🔧 常见问题

### Q: API连接失败怎么办？
A: 
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 查看API配额是否用完
4. 运行 `python test_components.py` 诊断问题

### Q: 收集不到关键词？
A: 
1. 检查关键词过滤规则
2. 确认监控的平台有相关内容
3. 调整 `keywords_to_track` 配置

### Q: Notion同步失败？
A: 
1. 确认数据库ID正确
2. 检查集成权限
3. 验证数据库字段结构

### Q: 应用运行缓慢？
A: 
1. 减少 `max_posts_per_subreddit` 数量
2. 增加API请求间隔
3. 检查网络连接速度

## 📝 使用技巧

### 1. 优化关键词收集
- 定期更新监控的子版块列表
- 调整关键词过滤规则
- 设置合适的收集频率

### 2. 趋势分析
- 关注7天趋势评分 > 50的关键词
- 比较不同时间段的趋势变化
- 结合KGR值选择低竞争关键词

### 3. SEO优化
- 优先选择KGR < 0.25的关键词
- 避免大站竞争激烈的关键词
- 关注有图片搜索结果的关键词

### 4. 数据管理
- 定期清理过期数据
- 创建Notion视图过滤关键词
- 导出数据进行进一步分析

## 🔄 定期维护

### 每周
- 检查API配额使用情况
- 更新关键词过滤规则
- 清理无效关键词

### 每月
- 更新依赖包: `pip install -r requirements.txt --upgrade`
- 备份配置文件
- 分析收集效果并优化

## 📞 获取帮助

### 文档资源
- README.md - 详细使用说明
- PROJECT_OVERVIEW.md - 项目架构说明
- 代码注释 - 技术实现细节

### 故障排除
1. 查看 `app.log` 日志文件
2. 运行 `python test_components.py` 诊断
3. 检查配置文件格式

### 技术支持
- 提交GitHub Issue
- 查看项目文档
- 参考示例配置

---

🎉 **恭喜！您已经成功设置了H5游戏关键词猎手。开始探索热门游戏关键词吧！**
