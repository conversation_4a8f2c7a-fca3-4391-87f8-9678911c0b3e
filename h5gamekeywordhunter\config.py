﻿import json
import os
from typing import Dict, Any

class Config:
    def __init__(self, config_file: str = "config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {self.config_file} 未找到")
        except json.JSONDecodeError:
            raise ValueError(f"配置文件 {self.config_file} 格式错误")
    
    def get_reddit_config(self) -> Dict[str, str]:
        """获取Reddit配置"""
        return self.config.get("reddit", {})
    
    def get_youtube_config(self) -> Dict[str, str]:
        """获取YouTube配置"""
        return self.config.get("youtube", {})
    
    def get_notion_config(self) -> Dict[str, str]:
        """获取Notion配置"""
        return self.config.get("notion", {})
    
    def get_google_search_config(self) -> Dict[str, str]:
        """获取Google搜索配置"""
        return self.config.get("google_search", {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get("monitoring", {})
    
    def get_scheduling_config(self) -> Dict[str, str]:
        """获取调度配置"""
        return self.config.get("scheduling", {})
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        required_sections = ["reddit", "youtube", "notion", "monitoring"]
        for section in required_sections:
            if section not in self.config:
                print(f"缺少配置节: {section}")
                return False
        
        # 检查必要的API密钥
        reddit_config = self.get_reddit_config()
        if not all(key in reddit_config for key in ["client_id", "client_secret"]):
            print("Reddit配置不完整")
            return False
        
        youtube_config = self.get_youtube_config()
        if "api_key" not in youtube_config:
            print("YouTube API密钥缺失")
            return False
        
        notion_config = self.get_notion_config()
        if not all(key in notion_config for key in ["token", "database_id"]):
            print("Notion配置不完整")
            return False
        
        return True

# 全局配置实例
config = Config()
